package com.jnby.mallasset.job;

import com.jnby.mallasset.remote.task.ExecuteOrderTask;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 通知异步执行的结果
 */
//@Component
@Slf4j
//@AllArgsConstructor
public class NotifyExecuteResultJob extends IJobHandler {

    private ExecuteOrderTask executeOrderTask;

//    @XxlJob("NotifyExecuteResultJob")
    @Override
    public void execute() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("通知异步执行的结果 NotifyExecuteResultJob 开始,入参：{}", jobParam);
        String res = executeOrderTask.execute();
        if(res.equals("success")){
            log.info("通知异步执行的结果 NotifyExecuteResultJob 执行成功");
        }else if(res.equals("none")){
            log.info("通知异步执行的结果 NotifyExecuteResultJob 空执行");
        }else {
            log.info("通知异步执行的结果 NotifyExecuteResultJob 执行失败");
        }
    }
}

