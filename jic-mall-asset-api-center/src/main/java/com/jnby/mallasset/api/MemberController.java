package com.jnby.mallasset.api;

import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.api.vo.MemberInfoRespVo;
import com.jnby.mallasset.dto.req.member.MemberRecordReq;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.module.service.IMemberBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
/**
 * 文件名: com.jnby.mallasset.api-MallCooController.java
 * 文件简介: 猫酷对外API
 *
 * <AUTHOR>
 * @date 2024/7/16 19:44
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@RequestMapping("/member")
@RestController
@AllArgsConstructor
@Slf4j
@Api(tags = "会员接口")
public class MemberController {

    private IMemberBizService memberBizService;

    @PostMapping("/register")
    @ApiOperation(value = "会员注册")
    public ResponseResult registerMember(@RequestBody MemberRegisterReq memberRegisterReq) {
        return memberBizService.registerMember(memberRegisterReq);
    }

    @PostMapping("/query")
    @ApiOperation(value = "会员查询")
    public ResponseResult queryMember(@RequestBody MemberRegisterReq memberRegisterReq) {
        log.info("会员查询入参req={}", JSON.toJSONString(memberRegisterReq));
        ResponseResult responseResult = memberBizService.queryMember(memberRegisterReq);
        log.info("会员查询回参resp={}", JSON.toJSONString(responseResult));
        return responseResult;
    }

    @PostMapping("/info")
    @ApiOperation(value = "对外统一会员查询")
    public ResponseResult<MemberInfoRespVo> memberInfo(@RequestBody MemberRegisterReq memberRegisterReq) {
        log.info("对外统一会员查询入参req={}", JSON.toJSONString(memberRegisterReq));
        ResponseResult<MemberInfoRespVo> responseResult = memberBizService.memberInfo(memberRegisterReq);
        log.info("对外统一会员查询回参resp={}", JSON.toJSONString(responseResult));
        return responseResult;
    }

    @PostMapping("/record")
    @ApiOperation(value = "收银台会员记录")
    public ResponseResult<Boolean> memberRecord(@RequestBody MemberRecordReq memberRegisterReq) {
        log.info("会员记录入参req={}", JSON.toJSONString(memberRegisterReq));
        ResponseResult<Boolean> responseResult = memberBizService.memberRecord(memberRegisterReq);
        log.info("会员记录回参resp={}", JSON.toJSONString(responseResult));
        return responseResult;
    }

}
