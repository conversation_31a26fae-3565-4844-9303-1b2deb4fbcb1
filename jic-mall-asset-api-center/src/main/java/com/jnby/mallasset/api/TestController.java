package com.jnby.mallasset.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Preconditions;
import com.jnby.common.ResponseResult;
import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.cache.RedisTemplateUtil;
import com.jnby.mallasset.module.mapper.box.CashierMallAssetConfigMapper;
import com.jnby.mallasset.module.mapper.box.CashierMallStoreConfigMapper;
import com.jnby.mallasset.module.model.CashierMallAssetConfig;
import com.jnby.mallasset.module.model.CashierMallStoreConfig;
import com.jnby.mallasset.module.service.IAssetBizService;
import com.jnby.mallasset.module.service.ICashierPayConfigService;
import com.jnby.mallasset.remote.mallcoo.IMallCooRemoteHttpApi;
import com.jnby.mallasset.remote.mallcoo.MallCooUtils;
import com.jnby.mallasset.remote.mallcoo.entity.BaseMallCooResp;
import com.jnby.mallasset.remote.mallcoo.entity.PointsListDetailReqEntity;
import com.jnby.mallasset.remote.mallcoo.entity.PointsListDetailRespEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import retrofit2.Call;
import retrofit2.Response;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RequestMapping("/test")
@RestController
@AllArgsConstructor
@Api(tags = "测试接口")
public class TestController {

    private static final Logger log = LoggerFactory.getLogger(TestController.class);
    @Autowired
    private CashierMallAssetConfigMapper cashierMallAssetConfigMapper;
    @Autowired
    private CashierMallStoreConfigMapper cashierMallStoreConfigMapper;
    @Autowired
    private RedisPoolUtil redisPoolUtil;
    @Autowired
    private IMallCooRemoteHttpApi mallCooRemoteHttpApi;
    @Autowired
    private IAssetBizService assetBizService;
    @Resource
    private ICashierPayConfigService cashierPayConfigService;
    @PostMapping("/flushPayConfigCache")
    @ApiOperation(value = "刷新门店支付方式配置")
    public ResponseResult flushPayConfigCache(@RequestBody String requestBody) {
        JSONObject jsonObject = JSON.parseObject(requestBody);
        String storeId = jsonObject.getString("bj_store_id");
        Preconditions.checkArgument(StringUtils.isNotBlank(storeId), "门店code必传");
        log.info("刷新门店支付方式配置 入参:{}", JSON.toJSONString(storeId));
        assetBizService.flushPayConfigCache(storeId);
        return ResponseResult.success();
    }

//    @PostMapping("/mergePayConfigData")
//    @ApiOperation(value = "同步支付配置信息")
//    public ResponseResult mergePayConfigData() {
//        log.info("开始同步");
//        cashierPayConfigService.migrateDataFromPaymentToPayConfig();
//        log.info("同步结束");
//        return ResponseResult.success();
//    }

    @PostMapping("/testMallAssetConfig")
    @ApiOperation(value = "测试资产配置")
    public ResponseResult testMallAssetConfig() {
        List<CashierMallAssetConfig> cashierMallAssetConfigs = cashierMallAssetConfigMapper.selectList(null);
        List<CashierMallStoreConfig> cashierMallStoreConfigs = cashierMallStoreConfigMapper.selectList(null);
        CashierMallStoreConfig cashierMallStoreConfig = cashierMallStoreConfigMapper.selectByStoreId("5DA52132");
        return ResponseResult.success();
    }

    @PostMapping("/testRedis")
    @ApiOperation(value = "测试Redis")
    public ResponseResult testRedis() {
        String uuid = "df007c1a-b1d9-49d4-bc3b-ad2d187c1a1f";
        String key = "mall_asset:" + uuid;
        String value = uuid;
        RedisTemplateUtil.setex(redisPoolUtil, key, value, 60);
        return ResponseResult.success();
    }

    @PostMapping("/listPoints")
    @ApiOperation(value = "获取猫酷积分")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号", required = true, paramType = "query"),
            @ApiImplicitParam(name = "bjStoreId", value = "伯俊门店CODE", required = true, paramType = "query")
    })
    public ResponseResult<PointsListDetailRespEntity> listPoints(String bjStoreId, String phone) {
        CashierMallStoreConfig cashierMallStoreConfig = cashierMallStoreConfigMapper.selectByStoreId(bjStoreId);
        if (cashierMallStoreConfig == null) {
            return ResponseResult.error(632,"门店不存在");
        }
        PointsListDetailReqEntity req = PointsListDetailReqEntity.builder().Mobile(phone).PageIndex(1).PageSize(100).build();
        String platformAppId = cashierMallStoreConfig.getPlatformAppId();
        String platformPublicKey = cashierMallStoreConfig.getPlatformPublicKey();
        String platformPrivateKey = cashierMallStoreConfig.getPlatformPrivateKey();
        try {
            Map<String, String> header = MallCooUtils.getHeaders(platformAppId, platformPublicKey, platformPrivateKey, JSON.toJSONString(req));
            Call<BaseMallCooResp<PointsListDetailRespEntity>> call = mallCooRemoteHttpApi.listScoreRecords(header, req);
            Response<BaseMallCooResp<PointsListDetailRespEntity>> execute = call.execute();
            boolean successful = execute.isSuccessful();
            if (!successful) {
                return ResponseResult.error(636,"获取猫酷积分失败");
            }
            BaseMallCooResp<PointsListDetailRespEntity> body = execute.body();
            if (body.getCode() == 1) {
                return ResponseResult.success(body.getData());
            } else {
                return ResponseResult.error(634, body.getMessage());
            }
        } catch (Exception e) {
            log.error("获取header失败", e);
            return ResponseResult.error(635,"获取header失败");
        }
    }
}
