package com.jnby.mallasset.api;

import com.alibaba.fastjson.JSON;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.dto.req.payconfig.AdminPayDefaultConfigReq;
import com.jnby.mallasset.dto.req.payconfig.AdminPayStoreConfigSearchReq;
import com.jnby.mallasset.dto.res.payconfig.AdminPayDefaultConfigResp;
import com.jnby.mallasset.dto.res.payconfig.AdminPayStoreConfigSearchResp;
import com.jnby.mallasset.module.service.ICashierPayConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequestMapping("/admin/pay/config/api")
@RestController
@Slf4j
@Api(tags = "管理员支付配置")
public class AdminPayConfigController {

    @Autowired
    private ICashierPayConfigService cashierPayConfigService;

    @PostMapping("/getDefaultStorePayConfigs")
    @ApiOperation(value = "获取默认配置")
    public ResponseResult<List<AdminPayDefaultConfigResp>> getDefaultStorePayConfigs(@RequestBody CommonRequest<AdminPayDefaultConfigReq> request) {
        log.info("获取默认门店支付配置请求 入参:{}", JSON.toJSONString(request));
        AdminPayDefaultConfigReq req = request.getRequestData();
        List<AdminPayDefaultConfigResp> result = cashierPayConfigService.getDefaultStorePayConfigs(req);
        log.info("获取默认门店支付配置请求 回参:{}", JSON.toJSONString(result));
        return ResponseResult.success(result, request.getPage());
    }

    @PostMapping("/searchStorePayConfigs")
    @ApiOperation(value = "门店聚合列表")
    public ResponseResult<List<AdminPayStoreConfigSearchResp>> searchStorePayConfigs(@RequestBody CommonRequest<AdminPayStoreConfigSearchReq> request) {
        log.info("搜索门店支付配置请求 入参:{}", JSON.toJSONString(request));
        AdminPayStoreConfigSearchReq req = request.getRequestData();
        List<AdminPayStoreConfigSearchResp> result = cashierPayConfigService.searchStorePayConfigs(req, request.getPage());
        log.info("搜索门店支付配置请求 回参:{}", JSON.toJSONString(result));
        return ResponseResult.success(result, request.getPage());
    }
}
