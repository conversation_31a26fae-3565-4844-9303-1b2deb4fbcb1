package com.jnby.mallasset.dto.req.member;

import com.jnby.mallasset.api.dto.BaseReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("会员记录请求入参")
public class MemberRecordReq {

    @ApiModelProperty(value = "会员手机号", required = true)
    private String mobile;
    @ApiModelProperty(value = "会员昵称")
    private String nickName;
    @ApiModelProperty(value = "1-男；2-女；0-未知")
    private int sex;
    @ApiModelProperty(value = "是否老会员 1-老会员 0-新会员")
    private int isNew;
    @ApiModelProperty(value = "会员小程序openId")
    private String openId;
    @ApiModelProperty(value = "门店编码")
    private String storeCode;
    @ApiModelProperty(value = "会员来源：微信商场；BOX")
    private String memberResource;
}
