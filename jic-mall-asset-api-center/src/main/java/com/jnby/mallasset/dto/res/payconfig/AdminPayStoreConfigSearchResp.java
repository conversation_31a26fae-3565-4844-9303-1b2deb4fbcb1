package com.jnby.mallasset.dto.res.payconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 门店支付配置搜索响应
 */
@Data
@ApiModel("门店支付配置搜索响应")
public class AdminPayStoreConfigSearchResp {

    @ApiModelProperty(value = "伯俊门店CODE")
    private String bjStoreId;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "支付平台列表")
    private List<PayChannelInfo> payChannels;

    /**
     * 支付平台信息
     */
    @Data
    @ApiModel("支付平台信息")
    public static class PayChannelInfo {
        @ApiModelProperty(value = "支付平台：1=微信直连、2=收钱吧-线上支付、3=支付宝直连、4=电银、5=华润自研、6=银联-B扫C、7=收钱吧-轻POS支付、8=银联-轻POS支付、9=收钱吧-B扫C")
        private Integer payChannel;
    }
}
