package com.jnby.mallasset.dto.req.payconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 新增或编辑支付配置请求
 */
@Data
@ApiModel("新增或编辑支付配置请求")
public class AdminPayConfigSaveReq {

    @ApiModelProperty(value = "主键id，新增时不传，编辑时必传")
    private String id;

    @ApiModelProperty(value = "伯俊门店CODE", required = true)
    @NotBlank(message = "伯俊门店CODE不能为空")
    private String bjStoreId;

    @ApiModelProperty(value = "门店名称", required = true)
    @NotBlank(message = "门店名称不能为空")
    private String storeName;

    @ApiModelProperty(value = "业务类型：1=微商城、2=BOX、3=POS+、4=复购计划、5=POS+线上（离店）、6=储值卡", required = true)
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;

    @ApiModelProperty(value = "支付平台：1=微信直连、2=收钱吧-线上支付、3=支付宝直连、4=电银、5=华润自研、6=银联-B扫C、7=收钱吧-轻POS支付、8=银联-轻POS支付、9=收钱吧-B扫C", required = true)
    @NotNull(message = "支付平台不能为空")
    private Integer payChannel;

    @ApiModelProperty(value = "支付配置ID：对应支付中心的不同平台配置表的ID", required = true)
    @NotBlank(message = "支付配置ID不能为空")
    private String payConfigId;

}
