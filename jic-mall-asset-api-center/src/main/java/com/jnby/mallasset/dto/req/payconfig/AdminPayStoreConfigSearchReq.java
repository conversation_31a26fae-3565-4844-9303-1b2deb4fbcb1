package com.jnby.mallasset.dto.req.payconfig;

import com.jnby.common.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 门店支付配置搜索请求
 */
@Data
@ApiModel("门店支付配置搜索请求")
public class AdminPayStoreConfigSearchReq {
    @ApiModelProperty(value = "伯俊门店CODE（精确匹配）")
    private String bjStoreId;

    @ApiModelProperty(value = "门店名称（模糊匹配）")
    private String storeName;

    @ApiModelProperty(value = "支付平台：1=微信直连、2=收钱吧-线上支付、3=支付宝直连、4=电银、5=华润自研、6=银联-B扫C、7=收钱吧-轻POS支付、8=银联-轻POS支付、9=收钱吧-B扫C")
    private Integer payChannel;
}
