package com.jnby.mallasset.dto.res.payconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 默认门店支付配置响应
 */
@Data
@ApiModel("默认门店支付配置响应")
public class AdminPayDefaultConfigResp {

    @ApiModelProperty(value = "主键id")
    private String id;

    @ApiModelProperty(value = "伯俊门店CODE")
    private String bjStoreId;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "业务类型：1=微商城、2=BOX、3=POS+、4=复购计划、5=POS+线上（离店）、6=储值卡")
    private Integer businessType;

    @ApiModelProperty(value = "支付平台：1=微信直连、2=收钱吧-线上支付、3=支付宝直连、4=电银、5=华润自研、6=银联-B扫C、7=收钱吧-轻POS支付、8=银联-轻POS支付、9=收钱吧-B扫C")
    private Integer payChannel;

    @ApiModelProperty(value = "支付配置ID：对应支付中心的不同平台配置表的ID")
    private String payConfigId;

    @ApiModelProperty(value = "设备号")
    private String deviceNumber;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "更新时间")
    private String updateTime;
}
