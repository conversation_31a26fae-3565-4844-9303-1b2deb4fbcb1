package com.jnby.mallasset.remote.callback;


import com.jnby.mallasset.dto.req.OrderCallBackRequest;
import com.jnby.mallasset.dto.req.PrepareRefundCallBackReq;
import com.jnby.mallasset.dto.req.member.MemberRecordReq;
import com.jnby.mallasset.dto.res.OrderCallBackResponse;
import com.jnby.mallasset.remote.mallcoo.entity.*;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.HeaderMap;
import retrofit2.http.POST;

import java.util.Map;


/**
 * 文件名: com.jnby.mallasset.remote.mallcoo-IOrderCallbackRemoteHttpApi.java
 * 文件简介: 订单回调请求服务
 *
 * <AUTHOR>
 * @date 2024/7/19 10:57
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
public interface IOrderCallbackRemoteHttpApi {

    /**
     * 功能描述: 订单消费回调
     * 使用场景:
     *
     * @param orderCallBackRequest
     * @return retrofit2.Call<com.jnby.mallasset.remote.mallcoo.entity.BaseMallCooResp>
     * <AUTHOR>
     * @date 2024/7/18 21:15
     */
    @POST("notice/orderNotify")
    Call<OrderCallBackResponse> orderCallback(@Body OrderCallBackRequest orderCallBackRequest);
    /**
     * 功能描述: 订单售后回调
     * 使用场景:
     *
     * @param orderCallBackRequest
     * @return retrofit2.Call<com.jnby.mallasset.remote.mallcoo.entity.BaseMallCooResp>
     * <AUTHOR>
     * @date 2024/7/18 21:15
     */
    @POST("notice/refundOrderNotify")
    Call<OrderCallBackResponse> orderRefundCallback(@Body OrderCallBackRequest orderCallBackRequest);
    /**
     * 功能描述: 预退货请求
     * 使用场景:
     *
     * @param prepareRefundCallBackReq
     * @return retrofit2.Call<com.jnby.mallasset.remote.mallcoo.entity.BaseMallCooResp>
     * <AUTHOR>
     * @date 2024/7/18 21:15
     */
    @POST("notice/update/refund/pushStatus")
    Call<OrderCallBackResponse> prepareRefundCallback(@Body PrepareRefundCallBackReq prepareRefundCallBackReq);

    @POST("notice/memberRecord")
    Call<OrderCallBackResponse> pushMemberInfo(@Body MemberRecordReq memberRegisterReq);
}
