package com.jnby.mallasset.config;

import com.jnby.common.util.IdLeaf;
import com.jnby.mallasset.config.exception.MallException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@RefreshScope
@Component
@Slf4j
public class IdConfig {

    public String getPayConfigId() {
        return getDateId("JIC_MALL_ASSET_STORE_PAY_CONFIG_ID");
    }

    private String getDateId(String tag) {
        try {
            String dateId = IdLeaf.getDateId(tag);
            if (StringUtils.isBlank(dateId)) {
                throw new MallException("ID生成异常:生成ID为空");
            }
            if (dateId.length() > 38) {
                throw new MallException("ID生成异常:长度超长");
            }
            return dateId;
        } catch (Exception e) {
            log.error("ID服务异常", e);
            throw new MallException("ID服务异常");
        }
    }

}
