package com.jnby.mallasset.module.service.impl;

import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.api.vo.MemberInfoRespVo;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.member.MemberRecordReq;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;
import com.jnby.mallasset.dto.res.OrderCallBackResponse;
import com.jnby.mallasset.module.mapper.box.CashierMallStoreConfigMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.service.IMemberBizService;
import com.jnby.mallasset.remote.callback.IOrderCallbackRemoteHttpApi;
import com.jnby.mallasset.remote.mallcoo.entity.MemberQueryRespEntity;
import com.jnby.mallasset.remote.wanke.entity.WanKeMemberRespEntity;
import com.jnby.mallasset.remote.yintai.entity.YinTaiMemberRespEntity;
import com.jnby.mallasset.strategy.category.AbstractMemberService;
import com.jnby.mallasset.strategy.factory.MemberServiceFactory;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.strategy.platform.xiexin.XiexinVIpInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import javax.annotation.Resource;


/**
 * 文件名: com.jnby.mallasset.module.service.impl-MallCooBizServiceImpl.java
 * 文件简介:
 *
 * <AUTHOR>
 * @date 2024/7/16 20:08
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
@Slf4j
@Service
public class MemberBizServiceImpl implements IMemberBizService {
    @Resource
    private MemberServiceFactory memberServiceFactory;
    @Resource
    private CashierMallStoreConfigMapper cashierMallStoreConfigMapper;
    @Resource
    private XiexinVIpInfoService xiexinVIpInfoService;
    @Resource
    private IOrderCallbackRemoteHttpApi orderCallbackRemoteHttpApi;

    private final static int SUCCESS_CODE = 0;
    private final static String SUCCESS_MSG = "success";

    @Override
    public ResponseResult registerMember(MemberRegisterReq memberRegisterReq) {
        CashierMallAssetStoreRef cashierMallAssetStoreRef = cashierMallStoreConfigMapper.selectInfoByStoreId(memberRegisterReq.getStoreId());
        PlatformTypeEnum platformTypeEnum = PlatformTypeEnum.getEnumByCode(cashierMallAssetStoreRef.getApiPlatform());
        AbstractMemberService abstractMemberService = memberServiceFactory.router(platformTypeEnum, PlatformCategoryTypeEnum.MEMBER);
        return abstractMemberService.openCard(memberRegisterReq, cashierMallAssetStoreRef);
    }


    @Override
    public ResponseResult queryMember(MemberRegisterReq memberRegisterReq) {
        CashierMallAssetStoreRef cashierMallAssetStoreRef = cashierMallStoreConfigMapper.selectInfoByStoreId(memberRegisterReq.getStoreId());
        PlatformTypeEnum platformTypeEnum = PlatformTypeEnum.getEnumByCode(cashierMallAssetStoreRef.getApiPlatform());
        AbstractMemberService abstractMemberService = memberServiceFactory.router(platformTypeEnum, PlatformCategoryTypeEnum.MEMBER);
        return abstractMemberService.getMemberInfo(memberRegisterReq, cashierMallAssetStoreRef);
    }

    @Override
    public ResponseResult<MemberInfoRespVo> memberInfo(MemberRegisterReq memberRegisterReq) {
        ResponseResult<MemberInfoRespVo> responseResult = new ResponseResult<>();
        MemberInfoRespVo memberInfoRespVo = new MemberInfoRespVo();
        CashierMallAssetStoreRef cashierMallAssetStoreRef = cashierMallStoreConfigMapper.selectInfoByStoreId(memberRegisterReq.getStoreId());
        if(cashierMallAssetStoreRef != null){
            // 平台枚举
            Integer apiPlatform = cashierMallAssetStoreRef.getApiPlatform();
            PlatformTypeEnum platformTypeEnum = PlatformTypeEnum.getEnumByCode(apiPlatform);
            // 协信
            if(platformTypeEnum.getCode().equals(11)){
                return getXieXinMemberInfo(memberRegisterReq, cashierMallAssetStoreRef, responseResult);
            }
            AbstractMemberService abstractMemberService = memberServiceFactory.router(platformTypeEnum, PlatformCategoryTypeEnum.MEMBER);
            ResponseResult result = abstractMemberService.getMemberInfo(memberRegisterReq, cashierMallAssetStoreRef);
            switch (apiPlatform){
                // 猫酷
                case 1:
                    mallCooMemberInfo(result, memberInfoRespVo,responseResult);
                    break;
                // 万科
                case 7:
                    wanKeMemberInfo(memberRegisterReq, result, memberInfoRespVo,responseResult);
                    break;
                // 银泰
                case 2:
                    yinTaiMemberInfo(result, memberInfoRespVo, responseResult);
                    break;
                // 大悦城
                case 12:
                    daYueChengMemberInfo(memberRegisterReq,result, memberInfoRespVo, responseResult);
                    break;
                default:

            }
        }
        return responseResult;
    }

    @Override
    public ResponseResult<Boolean> memberRecord(MemberRecordReq memberRegisterReq) {
        ResponseResult<Boolean> result = new ResponseResult<>();
        try{
            Response<OrderCallBackResponse> execute = orderCallbackRemoteHttpApi.pushMemberInfo(memberRegisterReq).execute();
            OrderCallBackResponse response = execute.body();
            log.info("收银台会员记录返回结果：{}", JSON.toJSONString(response));
            if(response.getCode() == 1){
                result.setCode(200);
                result.setData(true);
                return result;
            }
        }catch (Exception e){
            log.error("收银台会员记录失败：",e);
        }
        result.setCode(200);
        result.setData(false);
        return result;
    }

    private void daYueChengMemberInfo(MemberRegisterReq memberRegisterReq,ResponseResult result, MemberInfoRespVo memberInfoRespVo, ResponseResult<MemberInfoRespVo> responseResult) {
        if(result.getCode() == 200){
            MemberQueryRespEntity memberQueryRespEntity = (MemberQueryRespEntity) result.getData();
            if(memberQueryRespEntity != null){
                memberInfoRespVo.setTel(memberRegisterReq.getMobile());
                memberInfoRespVo.setNickName(memberQueryRespEntity.getNickName());
                memberInfoRespVo.setCardNo(memberQueryRespEntity.getMallCardNo());
                memberInfoRespVo.setCardName(memberQueryRespEntity.getMallCardName());
                memberInfoRespVo.setCardTypeCode(memberQueryRespEntity.getMallCardCode());
                memberInfoRespVo.setCardTypeID(String.valueOf(memberQueryRespEntity.getMallCardTypeID()));
                memberInfoRespVo.setUserId(memberQueryRespEntity.getOpenUserID());
                responseResult.setCode(SUCCESS_CODE);
                responseResult.setMsg(SUCCESS_MSG);
                responseResult.setData(memberInfoRespVo);
            }else {
                responseResult.setCode(result.getCode());
                responseResult.setMsg(result.getMsg());
            }
        }else {
            responseResult.setCode(result.getCode());
            responseResult.setMsg(result.getMsg());
        }
    }

    private ResponseResult<MemberInfoRespVo> getXieXinMemberInfo(MemberRegisterReq memberRegisterReq, CashierMallAssetStoreRef cashierMallAssetStoreRef, ResponseResult<MemberInfoRespVo> responseResult) {
        MemberInfoRespVo respVo = xiexinVIpInfoService.getXieXinMemberInfo(memberRegisterReq, cashierMallAssetStoreRef);
        if(respVo != null){
            responseResult.setCode(SUCCESS_CODE);
            responseResult.setMsg(SUCCESS_MSG);
            responseResult.setData(respVo);
        }else {
            responseResult.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            responseResult.setMsg(SystemErrorEnum.UNKNOWN_ERROR.getErrorMsg());
        }
        return responseResult;
    }

    private void yinTaiMemberInfo(ResponseResult result, MemberInfoRespVo memberInfoRespVo, ResponseResult<MemberInfoRespVo> responseResult) {
        if(result.getCode() == 200){
            YinTaiMemberRespEntity yinTaiMemberRespEntity = (YinTaiMemberRespEntity) result.getData();
            if(yinTaiMemberRespEntity != null){
                memberInfoRespVo.setTel(yinTaiMemberRespEntity.getMobile());
                memberInfoRespVo.setCardNo(yinTaiMemberRespEntity.getVipId());
                memberInfoRespVo.setUserId(yinTaiMemberRespEntity.getVipId());
                memberInfoRespVo.setCardTypeCode(yinTaiMemberRespEntity.getLevelName());
                memberInfoRespVo.setCardTypeID(yinTaiMemberRespEntity.getLevelNum());
                memberInfoRespVo.setCardName(yinTaiMemberRespEntity.getLevelName());
                responseResult.setCode(SUCCESS_CODE);
                responseResult.setMsg(SUCCESS_MSG);
                responseResult.setData(memberInfoRespVo);
            }else {
                responseResult.setCode(result.getCode());
                responseResult.setMsg(result.getMsg());
            }
        }else {
            responseResult.setCode(result.getCode());
            responseResult.setMsg(result.getMsg());
        }
    }

    private void wanKeMemberInfo(MemberRegisterReq memberRegisterReq, ResponseResult result, MemberInfoRespVo memberInfoRespVo,ResponseResult<MemberInfoRespVo> responseResult) {
        if(result.getCode() == 200){
            WanKeMemberRespEntity wanKeMemberRespEntity = (WanKeMemberRespEntity) result.getData();
            if(wanKeMemberRespEntity != null){
                memberInfoRespVo.setTel(memberRegisterReq.getMobile());
                memberInfoRespVo.setCardNo(wanKeMemberRespEntity.getMemberId());
                memberInfoRespVo.setUserId(wanKeMemberRespEntity.getMemberId());
                memberInfoRespVo.setCardTypeCode(String.valueOf(wanKeMemberRespEntity.getRatingId()));
                memberInfoRespVo.setCardTypeID(String.valueOf(wanKeMemberRespEntity.getRatingId()));
                memberInfoRespVo.setCardName(wanKeMemberRespEntity.getRatingName());
                responseResult.setCode(SUCCESS_CODE);
                responseResult.setMsg(SUCCESS_MSG);
                responseResult.setData(memberInfoRespVo);
            }else {
                responseResult.setCode(result.getCode());
                responseResult.setMsg(result.getMsg());
            }
        }else{
            responseResult.setCode(result.getCode());
            responseResult.setMsg(result.getMsg());
        }
    }

    private void mallCooMemberInfo(ResponseResult result, MemberInfoRespVo memberInfoRespVo,ResponseResult<MemberInfoRespVo> responseResult) {
        if(result.getData() != null){
            MemberQueryRespEntity memberQueryRespEntity = (MemberQueryRespEntity) result.getData();
            if(memberQueryRespEntity != null){
                memberInfoRespVo.setTel(memberQueryRespEntity.getMobile());
                memberInfoRespVo.setCardNo(memberQueryRespEntity.getMallCardNo());
                memberInfoRespVo.setCardName(memberQueryRespEntity.getMallCardName());
                memberInfoRespVo.setCardTypeCode(memberQueryRespEntity.getMallCardCode());
                memberInfoRespVo.setCardTypeID(String.valueOf(memberQueryRespEntity.getMallCardTypeID()));
                memberInfoRespVo.setUserId(memberQueryRespEntity.getOpenUserID());
                responseResult.setCode(SUCCESS_CODE);
                responseResult.setMsg(SUCCESS_MSG);
                responseResult.setData(memberInfoRespVo);
            }else {
                responseResult.setCode(result.getCode());
                responseResult.setMsg(result.getMsg());
            }
        }else {
            responseResult.setCode(result.getCode());
            responseResult.setMsg(result.getMsg());
        }
    }

}
