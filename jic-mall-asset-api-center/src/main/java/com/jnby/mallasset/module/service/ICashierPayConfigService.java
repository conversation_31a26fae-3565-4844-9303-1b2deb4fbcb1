package com.jnby.mallasset.module.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jnby.common.Page;
import com.jnby.mallasset.dto.req.payconfig.AdminPayDefaultConfigReq;
import com.jnby.mallasset.dto.req.payconfig.AdminPayStoreConfigSearchReq;
import com.jnby.mallasset.dto.res.payconfig.AdminPayDefaultConfigResp;
import com.jnby.mallasset.dto.res.payconfig.AdminPayStoreConfigSearchResp;
import com.jnby.mallasset.module.model.CashierPayConfig;

import java.util.List;

/**
 * 收银台支付配置
 */
public interface ICashierPayConfigService extends IService<CashierPayConfig> {

    void migrateDataFromPaymentToPayConfig();

    /**
     * 获取门店为DEFAULT的所有业务类型、支付平台的数据
     */
    List<AdminPayDefaultConfigResp> getDefaultStorePayConfigs(AdminPayDefaultConfigReq req);

    /**
     * 根据条件聚合搜索分页查询门店支付配置
     */
    List<AdminPayStoreConfigSearchResp> searchStorePayConfigs(AdminPayStoreConfigSearchReq req, Page page);
}
