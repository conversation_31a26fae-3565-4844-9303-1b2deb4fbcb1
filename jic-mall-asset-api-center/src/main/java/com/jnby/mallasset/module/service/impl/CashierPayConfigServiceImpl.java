package com.jnby.mallasset.module.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import com.jnby.mallasset.dto.req.payconfig.AdminPayDefaultConfigReq;
import com.jnby.mallasset.dto.res.payconfig.AdminPayDefaultConfigResp;
import com.jnby.mallasset.dto.req.payconfig.AdminPayStoreConfigSearchReq;
import com.jnby.mallasset.dto.res.payconfig.AdminPayStoreConfigSearchResp;
import com.jnby.mallasset.convert.AdminPayConfigConvertor;
import com.jnby.mallasset.module.mapper.box.CashierPayConfigMapper;
import com.jnby.mallasset.module.mapper.box.CashierPaymentConfigMapper;
import com.jnby.mallasset.module.model.CashierPayConfig;
import com.jnby.mallasset.module.model.CashierPaymentConfig;
import com.jnby.mallasset.module.service.ICashierPayConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.swing.text.html.Option;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CashierPayConfigServiceImpl extends ServiceImpl<CashierPayConfigMapper, CashierPayConfig> implements ICashierPayConfigService {

    @Resource
    private CashierPaymentConfigMapper cashierPaymentConfigMapper;

    private AtomicInteger ID = new AtomicInteger(1);

    private static final String DEFAULT_STORE_ID = "DEFAULT";

    public void migrateDataFromPaymentToPayConfig() {
        int pageNum = 1;
        int pageSize = 100;

        while (true) {
            // 开始分页
            PageHelper.startPage(pageNum, pageSize);
            // 执行查询
            List<CashierPaymentConfig> paymentConfigs = cashierPaymentConfigMapper.selectList(null);

            if (paymentConfigs == null || paymentConfigs.isEmpty()) {
                break;
            }

            // 转换并插入数据
            List<CashierPayConfig> payConfigs = convertToPayConfigs(paymentConfigs);
            this.saveBatch(payConfigs);

            pageNum++;
        }
    }


    private List<CashierPayConfig> convertToPayConfigs(List<CashierPaymentConfig> paymentConfigs) {
        List<CashierPayConfig> payConfigs = new ArrayList<>();

        for (CashierPaymentConfig paymentConfig : paymentConfigs) {
            log.info("原配置数据={}", JSON.toJSONString(paymentConfig));
            CashierPayConfig payConfig = new CashierPayConfig();
            payConfig.setIsDelete(paymentConfig.getIsDelete());
            payConfig.setCreateTime(paymentConfig.getCreateTime());
            payConfig.setUpdateTime(paymentConfig.getUpdateTime());
            payConfig.setBjStoreId(paymentConfig.getBjStoreId());
            payConfig.setStoreName(paymentConfig.getStoreName());
            payConfig.setPayChannel(paymentConfig.getPayChannel());
            payConfig.setPayConfigId(paymentConfig.getPayConfigId());
            payConfig.setDeviceNumber(paymentConfig.getDeviceNumber());

            // 根据不同的 HAS_XXX 字段设置 BUSINESS_TYPE
            if (Boolean.TRUE.equals(paymentConfig.getHasWxmall())) {
                payConfig.setBusinessType(1); // 微商城
                payConfigs.add(copyObject(payConfig));
            }
            if (Boolean.TRUE.equals(paymentConfig.getHasPos())) {
                payConfig.setBusinessType(3); // POS+
                payConfigs.add(copyObject(payConfig));
            }
            if (Boolean.TRUE.equals(paymentConfig.getHasPosOnline())) {
                payConfig.setBusinessType(5); // POS+ 线上
                payConfigs.add(copyObject(payConfig));
            }
            if (Boolean.TRUE.equals(paymentConfig.getHasBox())) {
                payConfig.setBusinessType(2); // BOX
                payConfigs.add(copyObject(payConfig));
            }
            if (Boolean.TRUE.equals(paymentConfig.getHasStoreCard())) {
                payConfig.setBusinessType(6); // 储值卡
                payConfigs.add(copyObject(payConfig));
            }
        }
        log.info("转换后的数据={}", JSON.toJSONString(payConfigs));
        return payConfigs;
    }

    // 深拷贝方法
    private CashierPayConfig copyObject(CashierPayConfig source) {
        CashierPayConfig target = new CashierPayConfig();
        target.setId(String.valueOf(ID.getAndIncrement()));
        target.setIsDelete(source.getIsDelete());
        target.setCreateTime(source.getCreateTime());
        target.setUpdateTime(source.getUpdateTime());
        target.setBjStoreId(source.getBjStoreId());
        target.setStoreName(source.getStoreName());
        target.setPayChannel(source.getPayChannel());
        target.setPayConfigId(source.getPayConfigId());
        target.setDeviceNumber(source.getDeviceNumber());
        target.setBusinessType(source.getBusinessType());
        return target;
    }

    @Override
    public List<AdminPayDefaultConfigResp> getDefaultStorePayConfigs(AdminPayDefaultConfigReq req) {
        log.info("获取默认门店支付配置 入参:{}", JSON.toJSONString(req));

        LambdaQueryWrapper<CashierPayConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CashierPayConfig::getIsDelete, 0)
                .eq(CashierPayConfig::getBjStoreId, DEFAULT_STORE_ID);

        // 根据业务类型过滤
        if (req.getBusinessType() != null) {
            queryWrapper.eq(CashierPayConfig::getBusinessType, req.getBusinessType());
        }

        // 根据支付平台过滤
        if (req.getPayChannel() != null) {
            queryWrapper.eq(CashierPayConfig::getPayChannel, req.getPayChannel());
        }

        queryWrapper.orderByDesc(CashierPayConfig::getCreateTime);

        List<CashierPayConfig> configs = this.list(queryWrapper);
        List<AdminPayDefaultConfigResp> respList = Optional.of(configs).orElse(new ArrayList<>()).stream()
                .map(AdminPayConfigConvertor.INSTANCE::toDefaultConfig)
                .collect(Collectors.toList());

        log.info("获取默认门店支付配置 回参:{}", JSON.toJSONString(respList));
        return respList;
    }

    @Override
    public List<AdminPayStoreConfigSearchResp> searchStorePayConfigs(AdminPayStoreConfigSearchReq req, Page page) {
        log.info("搜索门店支付配置 入参:{}", JSON.toJSONString(req));
        QueryWrapper<CashierPayConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("distinct(BJ_STORE_ID) AS bjStoreId, CREATE_TIME AS createTime, STORE_NAME AS storeName");
        queryWrapper.lambda().eq(CashierPayConfig::getIsDelete, 0);
        // 精确匹配伯俊门店CODE
        if (StringUtils.isNotBlank(req.getBjStoreId())) {
            queryWrapper.lambda().eq(CashierPayConfig::getBjStoreId, req.getBjStoreId());
        }
        // 模糊匹配门店名称
        if (StringUtils.isNotBlank(req.getStoreName())) {
            queryWrapper.lambda().like(CashierPayConfig::getStoreName, req.getStoreName());
        }
        // 支付平台过滤
        if (req.getPayChannel() != null) {
            queryWrapper.lambda().eq(CashierPayConfig::getPayChannel, req.getPayChannel());
        }
        queryWrapper.lambda().ne(CashierPayConfig::getBjStoreId, DEFAULT_STORE_ID);
        queryWrapper.lambda().orderByDesc(CashierPayConfig::getCreateTime);

        com.github.pagehelper.Page<CashierPayConfig> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<CashierPayConfig> configs = this.list(queryWrapper);
        PageInfo<CashierPayConfig> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        if (CollectionUtils.isEmpty(configs)) {
            return Collections.emptyList();
        }

        // 根据门店CODE查询详细数据
        List<String> storeIds = configs.stream().map(CashierPayConfig::getBjStoreId).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<CashierPayConfig> detailQueryWrapper = new LambdaQueryWrapper<>();
        detailQueryWrapper.eq(CashierPayConfig::getIsDelete, 0)
                .in(CashierPayConfig::getBjStoreId, storeIds);
        log.info("搜索门店支付配置 入参:{}", storeIds);
        List<CashierPayConfig> detailConfigs = this.list(detailQueryWrapper);
        log.info("搜索门店支付配置 回参总数:{}", detailConfigs.size());

        // 按门店聚合数据
        Map<String, List<CashierPayConfig>> storeGroupMap = Optional.of(detailConfigs).orElse(Lists.newArrayList())
                .stream().collect(Collectors.groupingBy(CashierPayConfig::getBjStoreId));

        List<AdminPayStoreConfigSearchResp> respList = configs.stream().map(config->
                        AdminPayConfigConvertor.INSTANCE.toStoreConfigSearchResp(config, storeGroupMap.get(config.getBjStoreId())))
                .collect(Collectors.toList());

        // 对 respList.payChannels 的数据进行去重
        respList.forEach(resp -> {
            if (CollectionUtils.isNotEmpty(resp.getPayChannels())) {
                resp.setPayChannels(resp.getPayChannels().stream().distinct().collect(Collectors.toList()));
            }
        });

        log.info("搜索门店支付配置 回参总数:{}", respList);
        return respList;
    }
}
