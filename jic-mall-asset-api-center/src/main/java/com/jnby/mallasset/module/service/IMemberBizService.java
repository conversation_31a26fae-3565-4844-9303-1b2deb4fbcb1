package com.jnby.mallasset.module.service;

import com.jnby.common.ResponseResult;
import com.jnby.mallasset.api.vo.MemberInfoRespVo;
import com.jnby.mallasset.dto.req.member.MemberRecordReq;
import com.jnby.mallasset.dto.req.member.MemberRegisterReq;

/**
 * 文件名: com.jnby.mallasset.module.service-IMallCooBizService.java
 * 文件简介: 猫酷服务
 *
 * <AUTHOR>
 * @date 2024/7/16 20:18
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
public interface IMemberBizService {

    /**
     * 功能描述: 注册会员
     * 使用场景:
     *
     * @param memberRegisterReq
     * @return com.jnby.mallasset.remote.mallcoo.entity.BaseMallCooResp<com.jnby.mallasset.remote.mallcoo.entity.MemberCreateCardRespEntity>
     * <AUTHOR>
     * @date 2024/7/16 20:18
     */
    ResponseResult registerMember(MemberRegisterReq memberRegisterReq);

    /**
     * 功能描述: 查询会员
     * 使用场景:
     *
     * @param memberRegisterReq
     * @return com.jnby.common.ResponseResult
     * <AUTHOR>
     * @date 2024/7/27 11:02
     */
    ResponseResult queryMember(MemberRegisterReq memberRegisterReq);


    /**
     * 功能描述: 查询会员
     * 使用场景:
     *
     * @param memberRegisterReq
     * @return com.jnby.common.ResponseResult
     * <AUTHOR>
     * @date 2024/7/27 11:02
     */
    ResponseResult<MemberInfoRespVo> memberInfo(MemberRegisterReq memberRegisterReq);

    /**
     * 功能描述:
     * 使用场景:
     *
     * @param memberRegisterReq
     * @return com.jnby.common.ResponseResult<com.jnby.mallasset.api.vo.MemberInfoRespVo>
     * <AUTHOR>
     * @date 2025/8/21 11:13
     */
    ResponseResult<Boolean> memberRecord(MemberRecordReq memberRegisterReq);

}
