package com.jnby.mallasset.strategy.platform.xiexin;

import com.alibaba.fastjson.JSON;
import com.jnby.common.ResponseResult;
import com.jnby.common.util.IdLeaf;
import com.jnby.mallasset.config.exception.ResultCodeEnum;
import com.jnby.mallasset.config.exception.SystemErrorEnum;
import com.jnby.mallasset.dto.req.order.OrderConsumeReq;
import com.jnby.mallasset.dto.req.order.OrderRefundReq;
import com.jnby.mallasset.module.mapper.box.CashierMallOrderLogMapper;
import com.jnby.mallasset.module.model.CashierMallAssetStoreRef;
import com.jnby.mallasset.module.model.CashierMallOrderLog;
import com.jnby.mallasset.remote.xiexin.IXiexinSoapRemoteHttpApi;
import com.jnby.mallasset.remote.xiexin.entity.order.*;
import com.jnby.mallasset.remote.xiexin.entity.vip.*;
import com.jnby.mallasset.strategy.category.AbstractOrderService;
import com.jnby.mallasset.strategy.factory.PlatformCategoryTypeEnum;
import com.jnby.mallasset.strategy.factory.PlatformTypeAnnotation;
import com.jnby.mallasset.strategy.factory.PlatformTypeEnum;
import com.jnby.mallasset.util.DateUtil;
import com.jnby.mallasset.util.OrderNoUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 猫酷平台策略
 */
@Service
@PlatformTypeAnnotation(platform = PlatformTypeEnum.XIE_XIN, category = PlatformCategoryTypeEnum.ORDER)
@AllArgsConstructor
@Slf4j
public class XiexinOrderStrategyService extends AbstractOrderService {

    private IXiexinSoapRemoteHttpApi xiexinSoapRemoteHttpApi;
    private CashierMallOrderLogMapper cashierMallOrderLogMapper;

    @Override
    public ResponseResult pushOrder(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        ResponseResult commonResponse = new ResponseResult();
        log.info("协信订单消费输入参数：{}", JSON.toJSONString(orderConsumeReq));
        try{
            // 记录流水
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderConsumeReq, cashierMallAssetStoreRef);
            String platformPublicKey = cashierMallAssetStoreRef.getPlatformPublicKey();
            String[] pubKeys = platformPublicKey.split("-");
            String userId = pubKeys[0];
            String password = pubKeys[1];

            // 先获取会员id
            String codeStr = cashierMallAssetStoreRef.getIntegralSkuCode();
            String[] items = codeStr.split(",");
            String[] condTypes = items[0].split("-");
            XiexinVipSoapEnvelope req = initXiexinVipSoapEnvelope(orderConsumeReq.getMemberTel(), userId, password, condTypes);

            Response<XiexinVipResponseSoapEnvelope> execute = xiexinSoapRemoteHttpApi.getVipCard(req).execute();
            XiexinVipResponseSoapEnvelope resBody = execute.body();
            XiexinVipResponseResult vipResponseResult = resBody.getBody().getVipResponseResult();
            if(vipResponseResult != null && vipResponseResult.isVipCardResult()){
                String[] storeCodes = items[1].split("-");
                String storeCode = storeCodes[1];
                String[] hths = items[2].split("-");
                int hth = Integer.parseInt(hths[1]);
                int vipId = Integer.parseInt(vipResponseResult.getVipCard().getCardId());
                // 计算订单积分
                XiexinOrderSoapEnvelope orderSoapEnvelope = new XiexinOrderSoapEnvelope();
                XiexinOrderSoapHeader orderSoapHeader = new XiexinOrderSoapHeader();
                XiexinOrderRequestHeader orderRequestHeader = new XiexinOrderRequestHeader();
                orderRequestHeader.setUserId(userId);
                orderRequestHeader.setPassword(password);
                orderSoapHeader.setRequestHeader(orderRequestHeader);
                orderSoapEnvelope.setHeader(orderSoapHeader);

                initOrderSoapEnvelope(orderConsumeReq, hth, storeCode, vipId, orderSoapEnvelope);

                Response<XiexinOrderResponseSoapEnvelope> orderExecute = xiexinSoapRemoteHttpApi.saleBillCent(orderSoapEnvelope).execute();
                XiexinOrderResponseSoapEnvelope orderRespBody = orderExecute.body();
                XiexinOrderResponseResult orderResponseResult = orderRespBody.getBody().getOrderResponseResult();
                if(orderResponseResult != null && orderResponseResult.isSaleBillCentResult()){
                    // 上传POS
                    String[] updateTypes = items[3].split("-");
                    int updateType = Integer.parseInt(updateTypes[1]);
                    Double gainCent = orderResponseResult.getGainCent();
                    XiexinOrderPosSoapEnvelope posSoapEnvelope = new XiexinOrderPosSoapEnvelope();
                    posSoapEnvelope.setHeader(orderSoapHeader);

                    XiexinOrderPosSoapBody posSoapBody = new XiexinOrderPosSoapBody();
                    XiexinOrderPosRequestModel posRequestModel = new XiexinOrderPosRequestModel();
                    posRequestModel.setHth(hth);
                    posRequestModel.setVipId(vipId);
                    posRequestModel.setStoreCode(storeCode);
                    posRequestModel.setSaleMoney(orderConsumeReq.getOrdAmount());
                    posRequestModel.setUpdateType(updateType);
                    posRequestModel.setSaleDate(DateUtil.timeStamp3Date(orderConsumeReq.getOrdFinishTime(),null).replaceAll(" ","T"));
//                    Long seqNo = cashierMallOrderLogMapper.selectXinxieOrderSequese();
                    Long seqNo = Long.valueOf(IdLeaf.getId("SEQ_LIANYU_XIEXIN_ORDER_TAG"));
                    int billId = OrderNoUtils.generalXiexinOrderNo(seqNo);
                    posRequestModel.setBillId(billId);
                    posRequestModel.setUpdateCent(gainCent);
                    posSoapBody.setRequestModel(posRequestModel);
                    posSoapEnvelope.setBody(posSoapBody);

                    Response<XiexinOrderPosResponseSoapEnvelope> posExecute = xiexinSoapRemoteHttpApi.saleShopItem(posSoapEnvelope).execute();
                    XiexinOrderPosResponseSoapEnvelope posResponseSoapEnvelope = posExecute.body();
                    XiexinOrderPosResponseResult orderPosResponseResult = posResponseSoapEnvelope.getBody().getOrderPosResponseResult();
                    if(orderPosResponseResult != null && orderPosResponseResult.isSaleShopItemResult()){
                        // 更新执行标识
                        commonResponse.setMsg(ResultCodeEnum.SUCCESS.getMsg());
                        commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                        XiexinOrderRespEntity entity = new XiexinOrderRespEntity();
                        entity.setOrdNum(orderConsumeReq.getOrdNum());
                        entity.setExchangeId(orderConsumeReq.getOrdNum());
                        entity.setExchangeNo(orderConsumeReq.getOrdNum());
                        commonResponse.setData(entity);
                        cashierMallOrderLog.setIsExecute("Y");
                        cashierMallOrderLog.setOuterOrderId(String.valueOf(billId));
                        cashierMallOrderLog.setTradeCent(gainCent);
                        cashierMallOrderLogMapper.updateEntityByParam(cashierMallOrderLog);
                    }else {
                        commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
                        commonResponse.setMsg(orderPosResponseResult.getMsg());
                    }
                }else {
                    commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
                    commonResponse.setMsg(orderResponseResult.getMsg());
                }
            }else {
                commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
                commonResponse.setMsg(vipResponseResult.getMsg());
            }
        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 协信订单消费加积分失败",orderConsumeReq.getOrdNum(),e);
        }
        return commonResponse;
    }

    private void initOrderSoapEnvelope(OrderConsumeReq orderConsumeReq, int hth, String storeCode, int vipId, XiexinOrderSoapEnvelope orderSoapEnvelope) {
        XiexinOrderSoapBody orderSoapBody = new XiexinOrderSoapBody();
        XiexinOrderRequestModel orderRequestModel = new XiexinOrderRequestModel();
        orderRequestModel.setHth(hth);
        orderRequestModel.setStoreCode(storeCode);
        orderRequestModel.setVipId(vipId);
        orderRequestModel.setSaleMoney(orderConsumeReq.getOrdAmount());
        orderSoapBody.setRequestModel(orderRequestModel);
        orderSoapEnvelope.setBody(orderSoapBody);
    }

    private XiexinVipSoapEnvelope initXiexinVipSoapEnvelope(String memberTel, String userId, String password, String[] condTypes) {
        XiexinVipSoapEnvelope req = new XiexinVipSoapEnvelope();
        XiexinVipSoapHeader header = new XiexinVipSoapHeader();
        XiexinVipRequestHeader requestHeader = new XiexinVipRequestHeader();
        requestHeader.setUserId(userId);
        requestHeader.setPassword(password);
        header.setRequestHeader(requestHeader);
        req.setHeader(header);

        XiexinVipSoapBody body = new XiexinVipSoapBody();
        XiexinVipRequestModel requestModel = new XiexinVipRequestModel();
        requestModel.setCondType(condTypes[1]);
        requestModel.setCondValue(memberTel);
        body.setRequestModel(requestModel);
        req.setBody(body);
        return req;
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderConsumeReq orderConsumeReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setExchangeNo(orderConsumeReq.getOrdNum());
        cashierMallOrderLog.setBjStoreId(orderConsumeReq.getStoreId());
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(1);
        cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    @Override
    public ResponseResult pushRefundOrder(OrderRefundReq orderRefundReq,CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        ResponseResult commonResponse = new ResponseResult();
        log.info("协信订单退货退积分输入参数：{}", JSON.toJSONString(orderRefundReq));
        try{
            // 记录流水
            String storeId = orderRefundReq.getStoreId();
            CashierMallOrderLog cashierMallOrderLog = recordCashierMallOrderLog(orderRefundReq, storeId, cashierMallAssetStoreRef);
            String platformPublicKey = cashierMallAssetStoreRef.getPlatformPublicKey();
            String[] pubKeys = platformPublicKey.split("-");
            String userId = pubKeys[0];
            String password = pubKeys[1];

            // 先获取会员id
            String codeStr = cashierMallAssetStoreRef.getIntegralSkuCode();
            String[] items = codeStr.split(",");
            String[] condTypes = items[0].split("-");
            XiexinVipSoapEnvelope req = initXiexinVipSoapEnvelope(orderRefundReq.getMemberTel(), userId, password, condTypes);

            Response<XiexinVipResponseSoapEnvelope> execute = xiexinSoapRemoteHttpApi.getVipCard(req).execute();
            XiexinVipResponseSoapEnvelope resBody = execute.body();
            XiexinVipResponseResult vipResponseResult = resBody.getBody().getVipResponseResult();
            if(vipResponseResult != null && vipResponseResult.isVipCardResult()){
                String[] storeCodes = items[1].split("-");
                String storeCode = storeCodes[1];
                String[] hths = items[2].split("-");
                int hth = Integer.parseInt(hths[1]);
                int vipId = Integer.parseInt(vipResponseResult.getVipCard().getCardId());

                // 上传POS
                String[] updateTypes = items[3].split("-");
                int updateType = Integer.parseInt(updateTypes[1]);
                // 计算积分比例
                BigDecimal ordAmount = orderRefundReq.getOrdAmount();
                BigDecimal refundAmount = orderRefundReq.getRefundAmount();
                BigDecimal rate = refundAmount.divide(ordAmount,4,RoundingMode.HALF_UP);
                CashierMallOrderLog orderLog = cashierMallOrderLogMapper.selectOrderLogByOrderNumAndExchangeId(orderRefundReq.getOrdNum(),orderRefundReq.getOrdNum());
                BigDecimal refundCent = rate.multiply(BigDecimal.valueOf(orderLog.getTradeCent())).setScale(1, RoundingMode.HALF_UP);

                XiexinOrderPosSoapEnvelope posSoapEnvelope = new XiexinOrderPosSoapEnvelope();
                XiexinOrderSoapHeader orderSoapHeader = new XiexinOrderSoapHeader();
                XiexinOrderRequestHeader orderRequestHeader = new XiexinOrderRequestHeader();
                orderRequestHeader.setUserId(userId);
                orderRequestHeader.setPassword(password);
                orderSoapHeader.setRequestHeader(orderRequestHeader);
                posSoapEnvelope.setHeader(orderSoapHeader);

                XiexinOrderPosSoapBody posSoapBody = new XiexinOrderPosSoapBody();
                XiexinOrderPosRequestModel posRequestModel = new XiexinOrderPosRequestModel();
                posRequestModel.setHth(hth);
                posRequestModel.setVipId(vipId);
                posRequestModel.setStoreCode(storeCode);
                posRequestModel.setSaleMoney(orderRefundReq.getRefundAmount().multiply(new BigDecimal(-1)));
                posRequestModel.setUpdateType(updateType);
                posRequestModel.setSaleDate(DateUtil.timeStamp3Date(orderRefundReq.getRefundTime(),null).replaceAll(" ","T"));
//                Long seqNo = cashierMallOrderLogMapper.selectXinxieOrderSequese();
                Long seqNo = Long.valueOf(IdLeaf.getId("SEQ_LIANYU_XIEXIN_ORDER_TAG"));
                int billId = OrderNoUtils.generalXiexinOrderNo(seqNo);
                posRequestModel.setBillId(billId);
                posRequestModel.setUpdateCent(-refundCent.doubleValue());
                posSoapBody.setRequestModel(posRequestModel);
                posSoapEnvelope.setBody(posSoapBody);

                Response<XiexinOrderPosResponseSoapEnvelope> posExecute = xiexinSoapRemoteHttpApi.saleShopItem(posSoapEnvelope).execute();
                XiexinOrderPosResponseSoapEnvelope posResponseSoapEnvelope = posExecute.body();
                XiexinOrderPosResponseResult orderPosResponseResult = posResponseSoapEnvelope.getBody().getOrderPosResponseResult();
                if(orderPosResponseResult != null && orderPosResponseResult.isSaleShopItemResult()){
                    // 更新执行标识
                    commonResponse.setMsg(ResultCodeEnum.SUCCESS.getMsg());
                    commonResponse.setCode(ResultCodeEnum.SUCCESS.getCode());
                    XiexinOrderRespEntity entity = new XiexinOrderRespEntity();
                    entity.setOrdNum(orderRefundReq.getOrdNum());
                    entity.setExchangeId(orderRefundReq.getRefundNo());
                    entity.setExchangeNo(orderRefundReq.getRefundNo());
                    commonResponse.setData(entity);
                    cashierMallOrderLog.setIsExecute("Y");
                    cashierMallOrderLog.setOuterOrderId(String.valueOf(billId));
                    cashierMallOrderLog.setTradeCent(refundCent.doubleValue());
                    cashierMallOrderLogMapper.updateEntityByParam(cashierMallOrderLog);
                }else {
                    commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
                    commonResponse.setMsg(orderPosResponseResult.getMsg());
                }
            }else {
                commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
                commonResponse.setMsg(vipResponseResult.getMsg());
            }

        }catch (Exception e){
            commonResponse.setCode(SystemErrorEnum.UNKNOWN_ERROR.getErrorCode());
            commonResponse.setMsg(e.getMessage());
            log.error("{} 协信订单售后退积分失败",orderRefundReq.getOrdNum(),e);
        }
        return commonResponse;
    }

    private CashierMallOrderLog recordCashierMallOrderLog(OrderRefundReq OrderRefundReq, String storeId, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        CashierMallOrderLog cashierMallOrderLog = new CashierMallOrderLog();
        cashierMallOrderLog.setOrdNum(OrderRefundReq.getOrdNum());
        cashierMallOrderLog.setExchangeId(OrderRefundReq.getRefundNo());
        cashierMallOrderLog.setExchangeNo(OrderRefundReq.getRefundNo());
        cashierMallOrderLog.setBjStoreId(storeId);
        cashierMallOrderLog.setMallId(cashierMallAssetStoreRef.getPlatformMallId());
        cashierMallOrderLog.setIsCallback("N");
        cashierMallOrderLog.setIsExecute("N");
        cashierMallOrderLog.setMallPlatform(cashierMallAssetStoreRef.getApiPlatform());
        cashierMallOrderLog.setMallStoreId(cashierMallAssetStoreRef.getPlatformMallStoreId());
        cashierMallOrderLog.setType(2);
        cashierMallOrderLogMapper.insertEntity(cashierMallOrderLog);
        return cashierMallOrderLog;
    }

    @Override
    public ResponseResult prepareRefundOrder(OrderRefundReq OrderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

    @Override
    public ResponseResult shopRefundOrder(OrderRefundReq orderRefundReq, CashierMallAssetStoreRef cashierMallAssetStoreRef) {
        return null;
    }

}
