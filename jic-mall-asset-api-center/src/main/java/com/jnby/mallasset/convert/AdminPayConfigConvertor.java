package com.jnby.mallasset.convert;

import com.jnby.mallasset.dto.req.payconfig.AdminPayConfigSaveReq;
import com.jnby.mallasset.dto.res.payconfig.AdminPayDefaultConfigResp;
import com.jnby.mallasset.dto.res.payconfig.AdminPayStoreConfigSearchResp;
import com.jnby.mallasset.module.model.CashierPayConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 管理员支付配置转换器
 */
@Mapper(config = ConvertConfig.class)
public interface AdminPayConfigConvertor {

    AdminPayConfigConvertor INSTANCE = Mappers.getMapper(AdminPayConfigConvertor.class);

    AdminPayStoreConfigSearchResp toStoreConfigSearchResp(CashierPayConfig config, List<CashierPayConfig> payChannels);

    @Mapping(target = "createTime", source = "createTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "updateTime", source = "updateTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    AdminPayDefaultConfigResp toDefaultConfig(CashierPayConfig config);

    /**
     * 请求DTO转实体
     */
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "isDelete", ignore = true)
    CashierPayConfig toEntity(AdminPayConfigSaveReq req);

}
