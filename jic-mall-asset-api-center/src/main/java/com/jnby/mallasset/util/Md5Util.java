package com.jnby.mallasset.util;

import com.google.gson.JsonObject;
import org.apache.commons.codec.digest.DigestUtils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

public class Md5Util {
    public static String getMd5Hash(String input) {
        return DigestUtils.md5Hex(input).toUpperCase();
    }

    public static String getMd5HashForLow(String input) {
        return DigestUtils.md5Hex(input).toLowerCase();
    }

    public static String md5(String string) {
        if (string == null) {
            return null;
        }
        char hexDigits[] = {
                '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };
        byte[] btInput = string.getBytes();
        try {
            /** 获得MD5摘要算法的 MessageDigest 对象 */
            MessageDigest mdInst = MessageDigest.getInstance("MD5");
            /** 使用指定的字节更新摘要 */
            mdInst.update(btInput);
            /** 获得密文 */
            byte[] md = mdInst.digest();
            /** 把密文转换成十六进制的字符串形式 */
            int j = md.length;
            char str[] = new char[j * 2];
            int k = 0;
            for (byte byte0 : md) {
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(str);
        } catch (NoSuchAlgorithmException e) {
            return null;
        }
    }

    /**
     * 字符串转16进制
     */
    public static String hex(String s) {
        String str = "";
        for (int i = 0; i < s.length(); i++) {
            int ch = s.charAt(i);
            String s4 = Integer.toHexString(ch);
            str = str + s4;
        }
        return str;
    }

//    public static void main(String[] args) {
//        long timestamp = System.currentTimeMillis()/1000;
//        System.out.println(timestamp);
//
//        Map<String,Object> jsonObject = new HashMap<>();
//        jsonObject.put("phone","15820584615");
//        jsonObject.put("phone_code","86");
//        jsonObject.put("reg_origin","SZ02");
//        jsonObject.put("nick_name","会员");
//        System.out.println("json=" + JsonUtil.toJson(jsonObject));
////
////
//        Map<String, String> params = new TreeMap<>();
//        params.put("content", JsonUtil.toJson(jsonObject));
////        params.put("phone_code", "86");
////        params.put("reg_origin", "SZ02");
////        params.put("nick_name", "会员");
//        params.put("timestamp", timestamp + "");
//        params.put("apiKey", "761175265309414");
//        params.put("interfaceId", "42943192bb75e59a4211");
//        System.out.println(genSignature(params));
//
//
//        String deSte = "efy3J8jab825Rel4IjowLCJtc2ciOiLmiJDlip8iLCJkYXRhIjp7InZpcF9jb2RlIjoiMTI1NDQ2MTUwMzcwOTkzOCIsIm1lbWJlcl9pZCI6IjEyNTQ0NjE1MDM3MDk5MzgifX0mbscd";
//        System.out.println(decode(deSte,"f38a85e4"));
//    }

    public static String genSignature(Map<String, String> params) {
        // 1. 添加apiSecret参数
        Map<String, String> paramsWithSecret = new TreeMap<>(params);
        paramsWithSecret.put("apiSecret", "xR3rF2f6");

        // 2. 移除已有的sign参数（如果存在）
        paramsWithSecret.remove("sign");

        // 3. 按key排序
        List<String> sortedKeys = new ArrayList<>(paramsWithSecret.keySet());
        Collections.sort(sortedKeys);

        // 4. 拼接字符串
        StringBuilder sb = new StringBuilder();
        for (String key : sortedKeys) {
            sb.append(key).append("+").append(paramsWithSecret.get(key));
        }
        System.out.println(sb.toString());
        return getMd5HashForLow(sb.toString());
    }

    public static String decode(String string, String skey) {
        if (string == null || string.isEmpty()) {
            return "";
        }
        if (skey == null) {
            skey = "";
        }

        // 替换字符（类似PHP的str_replace）
        String replaced = string.replace("mqtp", ":")
                .replace("mbscd", "=")
                .replace("nnddt", "+")
                .replace("abcde", "/")
                .replace("adted", "|");

        // 每2个字符分割成数组
        String[] strArr = new String[(replaced.length() + 1) / 2];
        for (int i = 0; i < replaced.length(); i += 2) {
            int endIndex = Math.min(i + 2, replaced.length());
            strArr[i / 2] = replaced.substring(i, endIndex);
        }

        // 处理skey
        for (int key = 0; key < skey.length(); key++) {
            if (key < strArr.length) {
                char value = skey.charAt(key);
                if (strArr[key].length() > 1 && strArr[key].charAt(1) == value) {
                    strArr[key] = String.valueOf(strArr[key].charAt(0));
                }
            }
        }

        // 拼接数组并base64解码
        String joined = String.join("", strArr);
        return BASE64Utils.decode(joined);
    }
}



