package com.jnby.mallasset.util;

import com.jnby.common.util.IdLeaf;

import java.util.Date;

/**
 * 文件名: com.jnby.mallasset.util-OrderNoUtils.java
 * 文件简介: 生成订单号
 *
 * <AUTHOR>
 * @date 2024/8/27 9:31
 *       Copyright (c) 1994 JNBY. All Rights Reserved
 */
public class OrderNoUtils {

    public static String generalOrderNo(Long seq) {
        long remainder = seq % 100000;
        String seqStr = Long.toString(remainder);
        String date = DateUtil.getShortStrDate(new Date());
        if (seqStr.length() == 1) {
            return date + "0000" + seqStr;
        }else if(seqStr.length() == 2){
            return date + "000" + seqStr;
        }else if(seqStr.length() == 3){
            return date + "00" + seqStr;
        }else if(seqStr.length() == 4){
            return date + "0" + seqStr;
        }else {
            return date + seqStr;
        }
    }

    public static Integer generalXiexinOrderNo(Long seq) {
        long remainder = seq % 1000;
        String seqStr = Long.toString(remainder);
        String result;
        String date = DateUtil.getShortStrDate(new Date()).substring(3);
        if (seqStr.length() == 1) {
            result = date + "000" + seqStr;
        }else if(seqStr.length() == 2){
            result = date + "00" + seqStr;
        }else if(seqStr.length() == 3){
            result = date + "0" + seqStr;
        }else {
            result = date + seqStr;
        }
        return Integer.parseInt(result);
    }

}
