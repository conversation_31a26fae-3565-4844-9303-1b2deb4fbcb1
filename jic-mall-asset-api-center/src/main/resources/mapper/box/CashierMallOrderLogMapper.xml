<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.mallasset.module.mapper.box.CashierMallOrderLogMapper">
    <insert id="insertEntity" parameterType="com.jnby.mallasset.module.model.CashierMallOrderLog">
        <selectKey keyProperty="count" resultType="java.lang.Integer" order="BEFORE">
            select count(*) count from jnby.cashier_mall_order_log where ORD_NUM = #{ordNum} and TYPE = #{type} and EXCHANGE_NO = #{exchangeNo}
        </selectKey>


        <if test="count == 0">
            insert into jnby.cashier_mall_order_log(ID,ORD_NUM,EXCHANGE_ID,EXCHAN<PERSON>_NO,MALL_ID,MALL_STORE_ID,MALL_PLATFORM,BJ_STORE_ID,IS_CALLBACK,IS_EXECUTE,TYPE)
            values(seq_cashier_mall_order_log.nextval,#{ordNum},#{exchangeId},#{exchangeNo},#{mallId},#{mallStoreId},#{mallPlatform},#{bjStoreId},#{isCallback},#{isExecute},#{type})
        </if>

        <if test="count > 0">
            update jnby.cashier_mall_order_log
            <set>
                <if test="mallId != null">
                    MALL_ID = #{mallId},
                </if>
                <if test="mallStoreId != null">
                    MALL_STORE_ID = #{mallStoreId},
                </if>
                <if test="mallPlatform != null">
                    MALL_PLATFORM = #{mallPlatform},
                </if>
                <if test="exchangeId != null">
                    EXCHANGE_ID = #{exchangeId},
                </if>
                <if test="exchangeNo != null and exchangeNo != ''">
                    EXCHANGE_NO = #{exchangeNo},
                </if>
                UPDATE_TIME = sysdate
            </set>
            where ORD_NUM = #{ordNum} and TYPE = #{type} and EXCHANGE_NO = #{exchangeNo}
        </if>
    </insert>

    <update id="updateEntityByParam" parameterType="com.jnby.mallasset.module.model.CashierMallOrderLog">
        update jnby.cashier_mall_order_log
        <set>
            <if test="isExecute != null and isExecute != ''">
                IS_EXECUTE = #{isExecute},
            </if>
            <if test="isCallback != null and isCallback != ''">
                IS_CALLBACK = #{isCallback},
            </if>
            <if test="exchangeId != null">
                EXCHANGE_ID = #{exchangeId},
            </if>
            <if test="exchangeNo != null and exchangeNo != ''">
                EXCHANGE_NO = #{exchangeNo},
            </if>
            <if test="promotionAmount != null">
                PROMOTION_AMOUNT = #{promotionAmount},
            </if>
            <if test="memberType != null">
                MEMBER_TYPE = #{memberType},
            </if>
            <if test="outerOrderId != null and outerOrderId != ''">
                OUTER_ORDER_ID = #{outerOrderId},
            </if>
            <if test="tradeCent != null">
                TRADE_CENT = #{tradeCent},
            </if>
            UPDATE_TIME = sysdate
        </set>
        where ORD_NUM = #{ordNum} and TYPE = #{type} and EXCHANGE_NO = #{exchangeNo}
    </update>

    <update id="updateOrderLogByParam" parameterType="com.jnby.mallasset.module.model.CashierMallOrderLog">
        update jnby.cashier_mall_order_log
        <set>
            <if test="isExecute != null and isExecute != ''">
                IS_EXECUTE = #{isExecute,jdbcType=VARCHAR},
            </if>
            <if test="isCallback != null and isCallback != ''">
                IS_CALLBACK = #{isCallback,jdbcType=VARCHAR},
            </if>
            <if test="updateExchangeId != null">
                EXCHANGE_ID = #{updateExchangeId,jdbcType=VARCHAR},
            </if>
            <if test="updateExchangeNo != null and updateExchangeNo != ''">
                EXCHANGE_NO = #{updateExchangeNo,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != ''">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="isZero != null and isZero != ''">
                IS_ZERO = #{isZero,jdbcType=VARCHAR},
            </if>
            <if test="memberType != null">
                MEMBER_TYPE = #{memberType,jdbcType=VARCHAR},
            </if>
            <if test="outerOrderId != null and outerOrderId != ''">
                OUTER_ORDER_ID = #{outerOrderId,jdbcType=VARCHAR},
            </if>
            <if test="memberId != null and memberId != ''">
                MEMBER_ID = #{memberId,jdbcType=VARCHAR},
            </if>
            <if test="refundAmount != null">
                REFUND_AMOUNT = #{refundAmount,jdbcType=DECIMAL},
            </if>
            <if test="qty != null">
                QTY = #{qty,jdbcType=DECIMAL},
            </if>
            UPDATE_TIME = sysdate
        </set>
        where ORD_NUM = #{ordNum} and TYPE = #{type} and EXCHANGE_NO = #{exchangeNo}
    </update>

    <select id="selectMallOrderLogList" resultType="com.jnby.mallasset.module.model.CashierMallOrderLog">
        select
            ORD_NUM ordNum,
            EXCHANGE_ID exchangeId,
            EXCHANGE_NO exchangeNo,
            MALL_ID mallId,
            MALL_STORE_ID mallStoreId,
            MALL_PLATFORM mallPlatform,
            BJ_STORE_ID bjStoreId,
            TYPE,
            REMARK remark
        from jnby.cashier_mall_order_log where IS_CALLBACK = 'N' and IS_EXECUTE = 'Y'
    </select>

    <update id="updateInfoByExchangeId" parameterType="com.jnby.mallasset.module.model.CashierMallOrderLog">
        update jnby.cashier_mall_order_log
        <set>
            <if test="isExecute != null and isExecute != ''">
                IS_EXECUTE = #{isExecute},
            </if>
            <if test="exchangeNo != null and exchangeNo != ''">
                EXCHANGE_NO = #{exchangeNo},
            </if>
            UPDATE_TIME = sysdate
        </set>
        where EXCHANGE_ID = #{exchangeId}
    </update>

    <select id="selectOrderLogByOrderNumAndExchangeId" resultType="com.jnby.mallasset.module.model.CashierMallOrderLog">
        SELECT
            EXCHANGE_ID exchangeId,EXCHANGE_NO exchangeNo,REFUND_AMOUNT refundAmount,PROMOTION_AMOUNT promotionAmount,
            MEMBER_TYPE memberType,IS_ZERO isZero,OUTER_ORDER_ID outerOrderId,TRADE_CENT tradeCent,UPDATE_TIME updateTime,
            MEMBER_ID memberId,QTY qty
        FROM jnby.cashier_mall_order_log where type = 1 and ORD_NUM = #{ordNum} and EXCHANGE_ID = #{exchangeId}
    </select>

    <select id="selectSumRefundAmount" resultType="com.jnby.mallasset.module.model.CashierMallOrderLog">
        select nvl(sum(refund_amount),0) refundAmount from jnby.cashier_mall_order_log where type = 2 and ORD_NUM = #{ordNum}
    </select>

    <update id="updateExchangeNoWithParam" parameterType="com.jnby.mallasset.module.model.CashierMallOrderLog">
        update jnby.cashier_mall_order_log set EXCHANGE_NO = #{exchangeNo},REMARK = #{remark},REFUND_AMOUNT = #{refundAmount} where ORD_NUM = #{ordNum} and EXCHANGE_ID = #{exchangeId}
                                                                                                                                and type = 1
    </update>

    <insert id="insertAddLog" parameterType="com.jnby.mallasset.module.model.CashierMallOrderLog">
        <selectKey keyProperty="count" resultType="java.lang.Integer" order="BEFORE">
            select count(*) count from jnby.cashier_mall_order_log where ORD_NUM = #{ordNum} and TYPE = #{type} and EXCHANGE_NO = #{exchangeNo}
        </selectKey>
        <if test="count == 0">
            insert into jnby.cashier_mall_order_log(ID,ORD_NUM,EXCHANGE_ID,EXCHANGE_NO,MALL_ID,MALL_STORE_ID,MALL_PLATFORM,BJ_STORE_ID,IS_CALLBACK,IS_EXECUTE,TYPE,REFUND_AMOUNT)
            values(seq_cashier_mall_order_log.nextval,#{ordNum},#{exchangeId},#{exchangeNo},#{mallId},#{mallStoreId},#{mallPlatform},#{bjStoreId},#{isCallback},#{isExecute},#{type},#{refundAmount})
        </if>

        <if test="count > 0">
            update jnby.cashier_mall_order_log
            <set>
                <if test="mallId != null">
                    MALL_ID = #{mallId},
                </if>
                <if test="mallStoreId != null">
                    MALL_STORE_ID = #{mallStoreId},
                </if>
                <if test="mallPlatform != null">
                    MALL_PLATFORM = #{mallPlatform},
                </if>
                <if test="exchangeId != null">
                    EXCHANGE_ID = #{exchangeId},
                </if>
                <if test="exchangeNo != null and exchangeNo != ''">
                    EXCHANGE_NO = #{exchangeNo},
                </if>
                UPDATE_TIME = sysdate
            </set>
            where ORD_NUM = #{ordNum} and TYPE = #{type} and EXCHANGE_NO = #{exchangeNo}
        </if>
    </insert>

    <select id="selectLogByOrdNumAndType" resultType="com.jnby.mallasset.module.model.CashierMallOrderLog">
        SELECT
            EXCHANGE_ID exchangeId,EXCHANGE_NO exchangeNo
        FROM jnby.cashier_mall_order_log where type = 1 and ORD_NUM = #{ordNum} and IS_EXECUTE = 'Y'
    </select>

    <select id="queryFirstRefundOrderByOrdNum" resultType="com.jnby.mallasset.module.model.CashierMallOrderLog">
        SELECT ID, ORD_NUM ordNum, EXCHANGE_ID exchangeId, EXCHANGE_NO exchangeNo, MEMBER_TYPE memberType
        FROM (SELECT ID, ORD_NUM, EXCHANGE_ID, EXCHANGE_NO, MEMBER_TYPE
              FROM jnby.cashier_mall_order_log
              where ORD_NUM = #{ordNum, jdbcType=VARCHAR}
                AND TYPE = 2 and IS_EXECUTE = 'Y'
              order by id)
        WHERE ROWNUM = 1
    </select>

    <select id="selectYinTaiOrderNoSeq" resultType="java.lang.Long">
        select jnby.SEQ_MALL_YINTAI_ORDER.nextval from dual
    </select>

    <select id="selectDrcOrderNoSeq" resultType="java.lang.Long">
        select jnby.SEQ_MALL_DRC_ORDER.nextval from dual
    </select>

    <select id="selectXinxieOrderSequese" resultType="java.lang.Long">
        select jnby.SEQ_LIANYU_XIEXIN_ORDER.NEXTVAL from dual
    </select>

    <select id="selectSumRefundProductQty" resultType="java.lang.Integer">
        select nvl(sum(qty),0) qty from jnby.cashier_mall_order_log where type = 2 and ORD_NUM = #{ordNum}
    </select>

    <select id="selectElevenOrderNoSeq" resultType="java.lang.Long">
        select JNBY.SEQ_MALL_ELEVEN_ORDER.nextval from dual
    </select>
</mapper>