FROM jnbyharbor.jnby.com/base-images/baseimagejdk8:v1.3

MAINTAINER box-group

RUN mkdir -p /jic-mall-asset/jic-mall-asset-api-center

WORKDIR /jic-mall-asset/jic-mall-asset-api-center

EXPOSE 9601

COPY target/jic-mall-asset-api-center.jar jic-mall-asset-api-center.jar

ENV TZ='Asia/Shanghai'

ENTRYPOINT ["java", "-Xmx1g", "-Xms1g", "-Xss512k", "-XX:+UseParallelGC","-Dreactor.netty.pool.leasingStrategy=lifo", "-jar", "jic-mall-asset-api-center.jar"]

CMD ["--spring.profiles.active=test"]


